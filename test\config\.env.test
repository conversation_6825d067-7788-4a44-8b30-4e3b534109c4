# Test Environment Configuration
# Database configuration for testing
DATABASE_URL="file:./test.db"

# Stripe test configuration
STRIPE_SECRET_KEY="sk_test_fake_key_for_testing"
STRIPE_WEBHOOK_SECRET="whsec_test_fake_webhook_secret"

# Email configuration for testing
SMTP_HOST="localhost"
SMTP_PORT="1025"
SMTP_USER="test"
SMTP_PASS="test"

# Application URLs for testing
SUCCESS_URL="http://localhost:3000/success"
CANCELED_URL="http://localhost:3000/canceled"

# JWT configuration for testing
JWT_SECRET="test_jwt_secret_key_for_testing_only"
JWT_EXPIRES_IN="1h"

# Node environment
NODE_ENV="test"
