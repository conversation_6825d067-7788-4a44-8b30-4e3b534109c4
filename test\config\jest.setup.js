/**
 * Jest Setup for Payment Module Tests
 *
 * Global setup and configuration for payment module tests.
 * Configures test environment, global mocks, and test utilities.
 */

// Global test timeout
jest.setTimeout(30000);

// Mock console methods to reduce noise in test output
global.console = {
  ...console,
  // Uncomment to suppress console.log in tests
  // log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Global test environment variables
process.env.NODE_ENV = 'test';
process.env.DATABASE_URL = 'file:./test.db';
process.env.STRIPE_SECRET_KEY = 'sk_test_mock_key';
process.env.STRIPE_WEBHOOK_SECRET = 'whsec_test_mock_secret';
process.env.JWT_SECRET = 'test_jwt_secret';

// Mock Date.now for consistent timestamps in tests
const mockDate = new Date('2024-01-01T00:00:00.000Z');
global.Date.now = jest.fn(() => mockDate.getTime());

// Global test utilities
global.testUtils = {
  // Helper to wait for async operations
  wait: (ms = 100) => new Promise((resolve) => setTimeout(resolve, ms)),

  // Helper to generate test IDs
  generateTestId: (prefix = 'test') =>
    `${prefix}_${Math.random().toString(36).substr(2, 9)}`,

  // Helper to create mock functions with default implementations
  createMockFunction: (defaultReturn = undefined) =>
    jest.fn().mockResolvedValue(defaultReturn),
};

// Global error handler for unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  // Don't exit the process in tests
});

// Global error handler for uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  // Don't exit the process in tests
});

// Setup global mocks for external dependencies
jest.mock('stripe', () => {
  return jest.fn().mockImplementation(() => ({
    checkout: {
      sessions: {
        create: jest.fn(),
        retrieve: jest.fn(),
      },
    },
    webhooks: {
      constructEvent: jest.fn(),
    },
    paymentIntents: {
      retrieve: jest.fn(),
    },
  }));
});

// Mock React Email components
jest.mock('@react-email/components', () => ({
  render: jest.fn().mockReturnValue('<html>Mock Email</html>'),
}));

// Email templates will be mocked in individual test files if needed

// Global beforeEach for all tests
beforeEach(() => {
  // Clear all mocks before each test
  jest.clearAllMocks();

  // Reset Date.now mock
  global.Date.now.mockReturnValue(mockDate.getTime());
});

// Global afterEach for all tests
afterEach(() => {
  // Additional cleanup if needed
});

// Global beforeAll for test suite
beforeAll(() => {
  // Setup that runs once before all tests
});

// Global afterAll for test suite
afterAll(() => {
  // Cleanup that runs once after all tests
});

// Custom Jest matchers
expect.extend({
  // Custom matcher to check if a value is a valid CUID
  toBeValidCuid(received) {
    const cuidRegex = /^c[a-z0-9]{24}$/;
    const pass = typeof received === 'string' && cuidRegex.test(received);

    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid CUID`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid CUID`,
        pass: false,
      };
    }
  },

  // Custom matcher to check if a value is a valid Stripe session ID
  toBeValidStripeSessionId(received) {
    const stripeSessionRegex = /^cs_[a-zA-Z0-9_]+$/;
    const pass =
      typeof received === 'string' && stripeSessionRegex.test(received);

    if (pass) {
      return {
        message: () =>
          `expected ${received} not to be a valid Stripe session ID`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid Stripe session ID`,
        pass: false,
      };
    }
  },

  // Custom matcher to check if a value is a valid email
  toBeValidEmail(received) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const pass = typeof received === 'string' && emailRegex.test(received);

    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid email`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid email`,
        pass: false,
      };
    }
  },

  // Custom matcher to check if a payment amount is in cents
  toBeValidPaymentAmount(received) {
    const pass =
      typeof received === 'number' &&
      received > 0 &&
      Number.isInteger(received);

    if (pass) {
      return {
        message: () =>
          `expected ${received} not to be a valid payment amount in cents`,
        pass: true,
      };
    } else {
      return {
        message: () =>
          `expected ${received} to be a valid payment amount in cents (positive integer)`,
        pass: false,
      };
    }
  },
});

// Export test utilities for use in test files
module.exports = {
  testUtils: global.testUtils,
};
