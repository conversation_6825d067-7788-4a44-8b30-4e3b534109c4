/**
 * Simple verification script to test service_id field inclusion
 * This script tests the transformer service directly to verify our fixes
 */

const { ApplicationTransformerService } = require('./dist/src/application/services/application-transformer.service');

// Mock application data that would come from the enhanced applications
const mockApplicationData = {
  id: 'app_123',
  application_number: 'IMM-2024-000001',
  service_type: 'immigration',
  service_id: 'service_456', // This should be included in the response
  service_name: 'Work Permit Application',
  status: 'draft',
  note: 'Test application note',
  priority_level: 'medium',
  current_step: '1',
  agent_details: [
    { id: 'agent_1', name: '<PERSON>', email: '<EMAIL>' }
  ],
  estimated_completion: null,
  assigned_to: null,
  created_at: new Date('2024-01-01'),
  updated_at: new Date('2024-01-01'),
  workflow_template: {
    id: 'template_123',
    name: 'Immigration Template',
    description: 'Template for immigration applications',
    workflowTemplate: [
      { stageOrder: 1, stageName: 'Initial Review' },
      { stageOrder: 2, stageName: 'Document Verification' }
    ],
  },
};

function testTransformerService() {
  console.log('🧪 Testing ApplicationTransformerService...\n');
  
  try {
    const transformerService = new ApplicationTransformerService();
    
    // Test transformApplicationListItem
    console.log('📋 Testing transformApplicationListItem:');
    const listResult = transformerService.transformApplicationListItem(mockApplicationData);
    
    console.log('✅ List transformation result:');
    console.log(`   - ID: ${listResult.id}`);
    console.log(`   - Service Type: ${listResult.service_type}`);
    console.log(`   - Service ID: ${listResult.service_id}`); // This should be present
    console.log(`   - Service Name: ${listResult.service_name}`);
    console.log(`   - Status: ${listResult.status}`);
    console.log(`   - Agent IDs count: ${listResult.agent_ids.length}`);
    console.log(`   - Number of Steps: ${listResult.numberOfSteps}`);
    
    if (listResult.service_id) {
      console.log('✅ service_id field is present in list response!\n');
    } else {
      console.log('❌ service_id field is missing from list response!\n');
    }
    
    // Test transformApplicationDetails
    console.log('📄 Testing transformApplicationDetails:');
    const detailResult = transformerService.transformApplicationDetails(mockApplicationData);
    
    console.log('✅ Detail transformation result:');
    console.log(`   - ID: ${detailResult.id}`);
    console.log(`   - Service Type: ${detailResult.service_type}`);
    console.log(`   - Service ID: ${detailResult.service_id}`); // This should be present
    console.log(`   - Service Name: ${detailResult.service_name}`);
    console.log(`   - Status: ${detailResult.status}`);
    console.log(`   - Note: ${detailResult.note}`);
    console.log(`   - Number of Steps: ${detailResult.numberOfSteps}`);
    
    if (detailResult.service_id) {
      console.log('✅ service_id field is present in detail response!\n');
    } else {
      console.log('❌ service_id field is missing from detail response!\n');
    }
    
    // Summary
    const listHasServiceId = !!listResult.service_id;
    const detailHasServiceId = !!detailResult.service_id;
    
    console.log('📊 Summary:');
    console.log(`   - List response includes service_id: ${listHasServiceId ? '✅' : '❌'}`);
    console.log(`   - Detail response includes service_id: ${detailHasServiceId ? '✅' : '❌'}`);
    
    if (listHasServiceId && detailHasServiceId) {
      console.log('\n🎉 SUCCESS: Both transformer methods include the service_id field!');
      console.log('The fix has been successfully implemented.');
    } else {
      console.log('\n❌ FAILURE: One or both transformer methods are missing the service_id field.');
      console.log('Additional fixes may be needed.');
    }
    
  } catch (error) {
    console.error('❌ Error testing transformer service:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Run the test
testTransformerService();
