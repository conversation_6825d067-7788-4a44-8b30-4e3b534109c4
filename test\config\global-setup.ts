/**
 * Jest Global Setup
 * Task 8: API Implementation and Documentation
 */

import { config } from 'dotenv';
import { join } from 'path';

export default async function globalSetup() {
  // Load test environment variables
  config({ path: join(__dirname, '../../.env.test') });

  console.log('🚀 Starting test environment setup...');

  // Set test environment variables
  process.env.NODE_ENV = 'test';
  process.env.JWT_SECRET = 'test-jwt-secret-for-testing';
  process.env.DATABASE_URL = process.env.TEST_DATABASE_URL || 'postgresql://test:test@localhost:5432/careerireland_test';

  // Mock external services globally
  process.env.SUPABASE_URL = 'https://test-supabase.com';
  process.env.SUPABASE_ANON_KEY = 'test-anon-key';
  process.env.MAIL_SENDER = '<EMAIL>';

  console.log('✅ Test environment setup completed');
}
