module.exports = {
  displayName: 'Application Module Tests',
  testMatch: ['<rootDir>/test/application/**/*.spec.ts'],
  moduleFileExtensions: ['js', 'json', 'ts'],
  rootDir: '../..',
  testEnvironment: 'node',
  transform: {
    '^.+\\.ts$': 'ts-jest',
    '^.+\\.js$': 'babel-jest',
  },
  collectCoverageFrom: [
    'src/application/**/*.(t|j)s',
    '!src/application/**/*.spec.ts',
    '!src/application/**/*.interface.ts',
    '!src/application/**/index.ts',
  ],
  coverageDirectory: 'coverage/application',
  coverageReporters: ['text', 'lcov', 'html'],
  setupFilesAfterEnv: ['<rootDir>/test/config/jest.setup.js'],
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
  },
};
